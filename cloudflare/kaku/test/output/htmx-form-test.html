<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - Choose a way to confirm it's you</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">Choose a way to confirm it's you</h1>
                    <p class="text-base mb-4"></p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission"}'>
<div class="prompt-container"></div>

    <div class="input-container">
      <div class="radio-fieldset">
        <div class="radio-legend">Choose a way to confirm it's you</div>
        <div class="radio-options">
          
        <div class="radio-group">
          <input
            class="radio-input"
            type="radio"
            id="radio_group_confirmation_methods-notification"
            name="radio_group_confirmation_methods"
            value="notification"
          >
          <label class="radio-label" for="radio_group_confirmation_methods-notification">
            <span class="radio-button"></span>
            <span class="radio-text">Notification on another device</span>
          </label>
        </div>
      
        <div class="radio-group">
          <input
            class="radio-input"
            type="radio"
            id="radio_group_confirmation_methods-whatsapp"
            name="radio_group_confirmation_methods"
            value="whatsapp"
          >
          <label class="radio-label" for="radio_group_confirmation_methods-whatsapp">
            <span class="radio-button"></span>
            <span class="radio-text">WhatsApp</span>
          </label>
        </div>
      
        <div class="radio-group">
          <input
            class="radio-input"
            type="radio"
            id="radio_group_confirmation_methods-text_message"
            name="radio_group_confirmation_methods"
            value="text_message"
          >
          <label class="radio-label" for="radio_group_confirmation_methods-text_message">
            <span class="radio-button"></span>
            <span class="radio-text">Text message</span>
          </label>
        </div>
      
        </div>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="submit"
        class="button-primary"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission", "clickId": "button_continue", "interaction": "submit"}'
      >
        Continue
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "extractionResult": {
    "screenInfo": {
      "errors": [],
      "controlVisibilityRules": [
        {
          "id": "radio_group_confirmation_methods",
          "status": "included",
          "reason": "This control is a radio button group, which is used for selecting a confirmation method."
        },
        {
          "id": "button_continue",
          "status": "included",
          "reason": "This is the primary submission button for the authentication flow."
        }
      ]
    },
    "controls": {
      "fields": [
        {
          "isLikelyDropdownReason": "The control is a radio button group, not a dropdown.",
          "id": "radio_group_confirmation_methods",
          "order": 1,
          "label": "Choose a way to confirm it's you",
          "isLikelyDropdown": false,
          "fieldControlType": "radio",
          "actiontype": "select",
          "name": "confirmation_method",
          "checked": false,
          "isDontAskAgainControl": false,
          "options": [
            {
              "label": "Notification on another device",
              "value": "notification"
            },
            {
              "label": "WhatsApp",
              "value": "whatsapp"
            },
            {
              "label": "Text message",
              "value": "text_message"
            }
          ]
        }
      ],
      "buttons": [
        {
          "id": "button_continue",
          "order": 2,
          "label": "Continue",
          "variant": "primary",
          "type": "submit",
          "actiontype": "click",
          "isDontAskAgainControl": false
        }
      ]
    }
  },
  "classificationResult": {
    "screenInfo": {
      "classificationReasoning": "The screen presents multiple options for confirming the user's identity, including sending a code via text message or WhatsApp, which are common methods for multi-factor authentication.",
      "authStateReasoning": "The user is in the process of verifying their identity through a second factor, indicating they have not yet fully authenticated.",
      "screenClass": "multi-factor-verification-screen",
      "instruction": "Choose a way to confirm it's you.",
      "title": "Choose a way to confirm it's you",
      "authState": "not-authenticated",
      "errors": null,
      "verificationCode": null
    }
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>